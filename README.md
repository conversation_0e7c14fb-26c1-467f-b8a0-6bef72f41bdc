# Finance Manager Mobile App

A comprehensive mobile finance management application built with React Native and Expo.

## Features

- 📱 Cross-platform mobile app (iOS & Android)
- 💰 Expense tracking and management
- 📊 Financial analytics and charts
- 🔐 Secure data storage
- 📄 Document management
- 🔔 Push notifications
- 🎨 Modern UI with React Native Paper

## Tech Stack

- **Framework**: React Native with Expo
- **State Management**: Redux Toolkit
- **Navigation**: React Navigation v6
- **UI Components**: React Native Paper
- **Charts**: React Native Chart Kit
- **Forms**: React Hook Form with Yup validation
- **HTTP Client**: Axios
- **Icons**: React Native Vector Icons

## Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Expo CLI
- iOS Simulator (for iOS development)
- Android Studio/Emulator (for Android development)

## Installation

1. Clone the repository:
```bash
git clone https://github.com/vishwa-glitch/fintech.git
cd fintech
```

2. Navigate to the mobile app directory:
```bash
cd project/finance-manager-mobile
```

3. Install dependencies:
```bash
npm install
```

## Running the App

### Development Mode

Start the Expo development server:
```bash
npm start
```

### Platform-specific Commands

- **iOS**: `npm run ios`
- **Android**: `npm run android`
- **Web**: `npm run web`

### Clear Cache

If you encounter issues, clear the cache:
```bash
npm run clear
```

## Project Structure

```
project/finance-manager-mobile/
├── src/                    # Source code
├── assets/                 # Static assets
├── App.tsx                 # Main app component
├── index.ts               # Entry point
├── package.json           # Dependencies
└── tsconfig.json          # TypeScript configuration
```

## Development

This project uses TypeScript for type safety and better development experience.

### Key Dependencies

- **@react-navigation**: Navigation library
- **@reduxjs/toolkit**: State management
- **expo**: Development platform
- **react-native-paper**: UI components
- **react-hook-form**: Form handling

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is private and proprietary.

## Contact

For questions or support, please contact the development team.
